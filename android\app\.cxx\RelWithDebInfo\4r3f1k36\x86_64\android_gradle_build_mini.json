{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\webrtc\\Flutter_Mobile_Screenshot_Receiver_WebRTC\\android\\app\\.cxx\\RelWithDebInfo\\4r3f1k36\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\webrtc\\Flutter_Mobile_Screenshot_Receiver_WebRTC\\android\\app\\.cxx\\RelWithDebInfo\\4r3f1k36\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}